# 包裹初始化功能测试指南

## 测试准备

### 1. 确保后端服务运行
```bash
cd JY-MONGO
npm run start:dev
```

### 2. 确保前端服务运行
```bash
cd JY-VUE3
npm run dev
```

### 3. 准备测试数据

创建测试用的Excel文件，包含以下字段：

| 包裹编码 | 仓库ID | SKU | 服装名称 | 初始数量 | 当前数量 | 位置码 | 服装ID | OEM服装ID | 供应商 | 货运单ID | 系列号 | 入库日期 |
|---------|--------|-----|----------|----------|----------|--------|--------|-----------|--------|----------|--------|----------|
| PKG001 | WH001 | CLT001_S | 测试服装1 | 100 | 100 | A01-01 | CLT001 | | 测试供应商 | TRP001 | 1 | 2024-01-01 |
| PKG002 | WH001 | CLT002_M | 测试服装2 | 200 | 180 | A01-02 | CLT002 | | 测试供应商 | TRP001 | 2 | 2024-01-01 |
| PKG003 | WH002 | OEM001_L | OEM测试服装 | 150 | 150 | B01-01 | | OEM001 | OEM供应商 | TRP002 | 1 | 2024-01-02 |

## 测试步骤

### 1. 访问发货信息页面
- 打开浏览器，访问 `http://localhost:3000/transportation/info`
- 确保页面正常加载，能看到"导入库存包裹"按钮

### 2. 测试模板下载
- 点击"导入库存包裹"按钮
- 在弹出的对话框中点击"下载Excel模板"
- 验证模板文件是否正确下载，包含正确的字段结构

### 3. 测试文件上传和解析
- 准备测试Excel文件（按照上述表格格式）
- 在对话框中上传Excel文件
- 验证文件解析是否正确，预览数据是否显示

### 4. 测试数据验证
- 尝试上传包含无效数据的Excel文件（如缺少必填字段）
- 验证系统是否正确提示错误信息

### 5. 测试包裹初始化
- 上传有效的Excel文件
- 点击"开始导入"按钮
- 观察进度条和导入结果
- 验证成功和失败的包裹数量统计

### 6. 验证数据库记录
- 检查 `packages` 集合是否创建了相应的记录
- 验证包裹数据的完整性和正确性
- 检查 `products` 集合是否自动创建了产品记录
- 验证服装库存数据是否正确更新

### 7. 测试兼容性
- 尝试使用现有的入库、出库、移库、盘存功能
- 验证初始化的包裹是否能正常参与这些操作
- 检查操作日志是否正确记录

## 预期结果

### 成功场景
- 包裹初始化成功，显示正确的统计信息
- 数据库中创建了对应的 packages 记录
- 自动创建了 products 记录（如果不存在）
- 服装库存数据正确更新
- 与现有仓库管理功能完全兼容

### 错误处理
- 重复包裹编码被正确识别和拒绝
- 无效仓库ID被正确识别和拒绝
- 缺少必填字段的记录被正确识别和拒绝
- 显示详细的错误信息和失败原因

## 常见问题排查

### 1. 上传文件解析失败
- 检查Excel文件格式是否正确（.xlsx 或 .xls）
- 验证字段名称是否与模板一致
- 确保没有空白行或格式错误

### 2. 包裹创建失败
- 检查包裹编码是否唯一
- 验证仓库ID是否存在于系统中
- 确保数量字段为有效数字

### 3. 服装库存更新失败
- 检查服装ID或OEM服装ID是否存在
- 验证数据库连接是否正常
- 查看后端日志获取详细错误信息

### 4. 前端显示异常
- 检查浏览器控制台是否有JavaScript错误
- 验证API接口是否正常响应
- 确保所有依赖组件正确导入

## 性能测试

### 大批量数据测试
- 准备包含1000+条记录的Excel文件
- 测试导入性能和内存使用情况
- 验证进度条和用户体验

### 并发测试
- 多用户同时进行包裹初始化
- 验证数据一致性和系统稳定性

## 安全测试

### 数据验证
- 尝试上传恶意Excel文件
- 测试SQL注入和XSS攻击防护
- 验证用户权限控制

## 回滚测试

### 数据回滚
- 记录初始化前的数据状态
- 测试如何回滚已初始化的包裹数据
- 验证数据一致性
