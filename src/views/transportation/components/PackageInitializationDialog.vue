<template>
  <el-dialog
    v-model="dialogVisible"
    title="导入库存包裹"
    width="60%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="package-initialization-dialog">
      <!-- 说明文档 -->
      <el-alert
        title="包裹初始化说明"
        type="info"
        :closable="false"
        show-icon
        class="mb-4"
      >
        <template #default>
          <div class="text-sm">
            <p>• 此功能用于初始化仓库中已存在的物理包裹库存</p>
            <p>• 请确保Excel中的数据准确无误，避免重复导入</p>
            <p>• 包裹编码必须唯一，系统会自动验证重复</p>
            <p>• 导入后的数据将与现有入库、出库、移库、盘存功能完全兼容</p>
          </div>
        </template>
      </el-alert>

      <!-- 模板下载区域 -->
      <div class="template-section mb-6">
        <h4 class="text-lg font-medium mb-3">第一步：下载Excel模板</h4>
        <el-button type="primary" @click="downloadTemplate" :icon="Download">
          下载Excel模板
        </el-button>
        <span class="ml-3 text-gray-500">请按照模板格式填写包裹数据</span>
      </div>

      <!-- 文件上传区域 -->
      <div class="upload-section mb-6">
        <h4 class="text-lg font-medium mb-3">第二步：上传Excel文件</h4>
        <el-upload
          ref="uploadRef"
          class="upload-demo"
          drag
          :auto-upload="false"
          :limit="1"
          :on-change="handleFileChange"
          :on-exceed="handleExceed"
          accept=".xlsx,.xls"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            将Excel文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              只能上传 .xlsx/.xls 文件，且不超过 10MB
            </div>
          </template>
        </el-upload>
      </div>

      <!-- 预览数据区域 -->
      <div v-if="previewData.length > 0" class="preview-section mb-6">
        <h4 class="text-lg font-medium mb-3">第三步：预览数据（前10条）</h4>
        <el-table :data="previewData.slice(0, 10)" border size="small" max-height="300">
          <el-table-column prop="package_code" label="包裹编码" width="120" />
          <el-table-column prop="warehouse_id" label="仓库ID" width="100" />
          <el-table-column prop="sku" label="SKU" width="120" />
          <el-table-column prop="clothing_name" label="服装名称" width="150" />
          <el-table-column prop="original_quantity" label="初始数量" width="100" />
          <el-table-column prop="current_quantity" label="当前数量" width="100" />
          <el-table-column prop="location_code" label="位置码" width="100" />
        </el-table>
        <div class="mt-2 text-sm text-gray-500">
          共 {{ previewData.length }} 条数据，仅显示前10条预览
        </div>
      </div>

      <!-- 进度条 -->
      <div v-if="importing" class="progress-section mb-4">
        <el-progress :percentage="importProgress" :status="importStatus" />
        <div class="mt-2 text-center text-sm text-gray-600">
          {{ importMessage }}
        </div>
      </div>

      <!-- 导入结果 -->
      <div v-if="importResult" class="result-section mb-4">
        <el-alert
          :title="importResult.message"
          :type="importResult.code === 200 ? 'success' : 'error'"
          :closable="false"
          show-icon
        >
          <template #default>
            <div class="text-sm">
              <p>总计请求: {{ importResult.data.total_requested }} 个包裹</p>
              <p>成功创建: {{ importResult.data.successful_created }} 个包裹</p>
              <p>创建失败: {{ importResult.data.failed_created }} 个包裹</p>
              <p>批次号: {{ importResult.data.batch_code }}</p>
              
              <div v-if="importResult.data.failed_packages.length > 0" class="mt-3">
                <p class="font-medium text-red-600">失败详情:</p>
                <ul class="list-disc list-inside">
                  <li v-for="failed in importResult.data.failed_packages" :key="failed.package_code" class="text-red-600">
                    {{ failed.package_code }}: {{ failed.reason }}
                  </li>
                </ul>
              </div>
            </div>
          </template>
        </el-alert>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" :disabled="importing">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleImport" 
          :disabled="previewData.length === 0 || importing"
          :loading="importing"
        >
          {{ importing ? '导入中...' : '开始导入' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Download, UploadFilled } from '@element-plus/icons-vue'
import * as XLSX from 'xlsx'
import { initializePackages } from '@/api/transportation'
import {
  validatePackageInitializationData,
  sanitizePackageInitializationData,
  formatValidationResult,
  generatePackageInitializationStats
} from '@/utils/packageInitializationValidator'

// Props
interface Props {
  visible: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'success': []
}>()

// 响应式数据
const uploadRef = ref()
const previewData = ref<any[]>([])
const importing = ref(false)
const importProgress = ref(0)
const importStatus = ref<'success' | 'exception' | undefined>(undefined)
const importMessage = ref('')
const importResult = ref<any>(null)

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 下载Excel模板
const downloadTemplate = () => {
  // 创建模板数据
  const templateData = [
    {
      '包裹编码': 'PKG001',
      '仓库ID': 'WH001', 
      'SKU': 'CLT001_S',
      '服装名称': '示例服装',
      '初始数量': 100,
      '当前数量': 100,
      '位置码': 'A01-01',
      '服装ID': 'CLT001',
      'OEM服装ID': '',
      '供应商': '示例供应商',
      '货运单ID': 'TRP001',
      '系列号': 1,
      '入库日期': '2024-01-01'
    }
  ]

  // 创建工作簿
  const wb = XLSX.utils.book_new()
  const ws = XLSX.utils.json_to_sheet(templateData)

  // 设置列宽
  const colWidths = [
    { wch: 15 }, // 包裹编码
    { wch: 10 }, // 仓库ID
    { wch: 15 }, // SKU
    { wch: 20 }, // 服装名称
    { wch: 10 }, // 初始数量
    { wch: 10 }, // 当前数量
    { wch: 12 }, // 位置码
    { wch: 12 }, // 服装ID
    { wch: 15 }, // OEM服装ID
    { wch: 15 }, // 供应商
    { wch: 12 }, // 货运单ID
    { wch: 8 },  // 系列号
    { wch: 12 }  // 入库日期
  ]
  ws['!cols'] = colWidths

  // 添加工作表到工作簿
  XLSX.utils.book_append_sheet(wb, ws, '包裹初始化模板')

  // 导出文件
  XLSX.writeFile(wb, '包裹初始化模板.xlsx')
  ElMessage.success('模板下载成功')
}

// 处理文件变化
const handleFileChange = (file: any) => {
  if (file.raw) {
    parseExcelFile(file.raw)
  }
}

// 处理文件超出限制
const handleExceed = () => {
  ElMessage.warning('只能上传一个文件')
}

// 解析Excel文件
const parseExcelFile = (file: File) => {
  const reader = new FileReader()
  
  reader.onload = (e: any) => {
    try {
      // 解析Excel数据
      const data = new Uint8Array(e.target.result)
      const workbook = XLSX.read(data, { type: 'array' })
      
      // 获取第一个工作表
      const firstSheetName = workbook.SheetNames[0]
      const worksheet = workbook.Sheets[firstSheetName]
      
      // 转换为JSON
      let jsonData = XLSX.utils.sheet_to_json(worksheet) as Record<string, any>[]
      
      // 过滤空白行
      jsonData = jsonData.filter((row) => {
        return Object.values(row).some((value) => {
          if (value === undefined || value === null) return false
          if (typeof value === 'string' && value.trim() === '') return false
          return true
        })
      })

      // 标准化字段名称
      const rawData = jsonData.map((item) => ({
        package_code: item['包裹编码'] || item['package_code'] || '',
        warehouse_id: item['仓库ID'] || item['warehouse_id'] || '',
        sku: item['SKU'] || item['sku'] || '',
        clothing_name: item['服装名称'] || item['clothing_name'] || '',
        original_quantity: Number(item['初始数量'] || item['original_quantity'] || 0),
        current_quantity: Number(item['当前数量'] || item['current_quantity'] || 0),
        location_code: item['位置码'] || item['location_code'] || '',
        clothing_id: item['服装ID'] || item['clothing_id'] || '',
        oem_clothing_id: item['OEM服装ID'] || item['oem_clothing_id'] || '',
        supplier: item['供应商'] || item['supplier'] || '',
        transportation_id: item['货运单ID'] || item['transportation_id'] || '',
        series_number: item['系列号'] || item['series_number'] || null,
        inbound_date: item['入库日期'] || item['inbound_date'] || ''
      }))

      // 清理和标准化数据
      const standardizedData = sanitizePackageInitializationData(rawData)

      // 验证数据
      const validationResult = validatePackageInitializationData(standardizedData)

      previewData.value = standardizedData
      importResult.value = null

      // 显示验证结果
      if (validationResult.errors.length > 0) {
        ElMessage.error(`数据验证失败：发现 ${validationResult.errors.length} 个错误`)
        console.error('数据验证错误:', formatValidationResult(validationResult))
      } else {
        const stats = generatePackageInitializationStats(standardizedData)
        ElMessage.success(`成功解析 ${standardizedData.length} 条数据，涉及 ${stats.warehouseCount} 个仓库，${stats.skuCount} 个SKU`)

        if (validationResult.warnings.length > 0) {
          ElMessage.warning(`发现 ${validationResult.warnings.length} 个警告，请检查数据`)
          console.warn('数据验证警告:', formatValidationResult(validationResult))
        }
      }
      
    } catch (error) {
      console.error('解析Excel文件失败:', error)
      ElMessage.error('解析Excel文件失败，请检查文件格式')
    }
  }
  
  reader.readAsArrayBuffer(file)
}

// 处理导入
const handleImport = async () => {
  if (previewData.value.length === 0) {
    ElMessage.warning('请先上传Excel文件')
    return
  }

  // 数据验证
  const validationResult = validatePackageInitializationData(previewData.value)

  if (!validationResult.isValid) {
    ElMessage.error(`数据验证失败：发现 ${validationResult.errors.length} 个错误`)
    console.error('验证错误详情:', formatValidationResult(validationResult))
    return
  }

  // 显示警告（如果有）
  if (validationResult.warnings.length > 0) {
    console.warn('验证警告详情:', formatValidationResult(validationResult))
  }

  try {
    await ElMessageBox.confirm(
      `确定要导入 ${previewData.value.length} 条包裹数据吗？此操作不可撤销。`,
      '确认导入',
      {
        confirmButtonText: '确定导入',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    importing.value = true
    importProgress.value = 0
    importStatus.value = undefined
    importMessage.value = '正在导入包裹数据...'

    // 模拟进度更新
    const progressInterval = setInterval(() => {
      if (importProgress.value < 90) {
        importProgress.value += 10
      }
    }, 200)

    // 调用API
    const response = await initializePackages({
      packages: previewData.value,
      operator: 'admin', // 可以从用户信息中获取
      notes: '批量包裹初始化'
    })

    clearInterval(progressInterval)
    importProgress.value = 100
    importStatus.value = response.code === 200 ? 'success' : 'exception'
    importMessage.value = response.message
    importResult.value = response

    if (response.code === 200) {
      ElMessage.success('包裹初始化成功')
      emit('success')
    } else {
      ElMessage.error('包裹初始化失败')
    }

  } catch (error: any) {
    importing.value = false
    importProgress.value = 0
    importStatus.value = 'exception'
    
    if (error === 'cancel') {
      importMessage.value = '用户取消导入'
    } else {
      console.error('包裹初始化失败:', error)
      importMessage.value = '导入失败'
      ElMessage.error(error.response?.data?.message || '包裹初始化失败')
    }
  } finally {
    importing.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  if (importing.value) {
    ElMessage.warning('正在导入中，请稍候...')
    return
  }
  
  // 重置数据
  previewData.value = []
  importResult.value = null
  importProgress.value = 0
  importStatus.value = undefined
  importMessage.value = ''
  
  // 清空上传组件
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
  
  emit('update:visible', false)
}
</script>

<style scoped>
.package-initialization-dialog {
  padding: 0 10px;
}

.template-section,
.upload-section,
.preview-section,
.progress-section,
.result-section {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 16px;
  background-color: #fafafa;
}

.upload-demo {
  width: 100%;
}

.dialog-footer {
  text-align: right;
}

.mb-3 {
  margin-bottom: 12px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mb-6 {
  margin-bottom: 24px;
}

.mt-2 {
  margin-top: 8px;
}

.mt-3 {
  margin-top: 12px;
}

.ml-3 {
  margin-left: 12px;
}

.text-sm {
  font-size: 14px;
}

.text-lg {
  font-size: 18px;
}

.font-medium {
  font-weight: 500;
}

.text-gray-500 {
  color: #6b7280;
}

.text-gray-600 {
  color: #4b5563;
}

.text-red-600 {
  color: #dc2626;
}

.text-center {
  text-align: center;
}

.list-disc {
  list-style-type: disc;
}

.list-inside {
  list-style-position: inside;
}
</style>
