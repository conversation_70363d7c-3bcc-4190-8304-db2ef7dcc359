import { Injectable, NotFoundException, HttpException, HttpStatus } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model } from 'mongoose'
import { Transportation } from '../../models/transportation.model'
import { TransportationDetail } from '../../models/transportationDetail.model'
// import { InboundOrderDetail } from '../../models/inboundOrder.model' // 已删除，使用新的仓库管理系统
import { Package } from '../../models/package.model'
import { Clothing } from '../../models/clothing.model'
import { OemClothing } from '../../models/oemClothing.model'
import { Warehouse } from '../../models/warehouse.model'
import { Product } from '../../models/product.model'
import {
  CreateTransportationDto,
  UpdateTransportationDto,
  QueryTransportationDto,
  CreateTransportationDetailDto,
  UpdateTransportationDetailDto,
  PackageInitializationDto,
  PackageInitializationResponseDto,
} from './dto'

@Injectable()
export class TransportationService {
  constructor(
    @InjectModel('Transportation')
    private readonly transportationModel: Model<Transportation>,
    @InjectModel('TransportationDetail')
    private readonly transportationDetailModel: Model<TransportationDetail>,
    // @InjectModel('InboundOrderDetail') // 已删除，使用新的仓库管理系统
    // private readonly inboundOrderDetailModel: Model<InboundOrderDetail>,
    @InjectModel('Package')
    private readonly packageModel: Model<Package>,
    @InjectModel('Clothing')
    private readonly clothingModel: Model<Clothing>,
    @InjectModel('OemClothing')
    private readonly oemClothingModel: Model<OemClothing>,
    @InjectModel('Warehouse')
    private readonly warehouseModel: Model<Warehouse>,
    @InjectModel('Product')
    private readonly productModel: Model<Product>
  ) {}

  // 创建发货信息
  async create(createTransportationDto: CreateTransportationDto): Promise<Transportation> {
    const transportation = new this.transportationModel(createTransportationDto)
    return transportation.save()
  }

  // 获取发货信息列表，支持分页和筛选
  async findAll(queryParams: QueryTransportationDto) {
    console.log('后端收到的查询参数：', JSON.stringify(queryParams))

    // 处理可能的数组参数格式问题
    const processedParams = { ...queryParams } as Record<string, any>

    // 检查并处理 transportation_years[] 格式的参数
    for (const key in queryParams as Record<string, any>) {
      if (key.endsWith('[]')) {
        const baseKey = key.slice(0, -2)
        processedParams[baseKey] = (queryParams as Record<string, any>)[key]
        delete processedParams[key]
      }
    }

    const {
      transportation_id,
      transportation_year,
      transportation_years,
      date_out_start,
      date_out_end,
      supplier,
      suppliers,
      page = 1,
      limit = 10,
    } = processedParams

    // 构建查询条件
    const filter: any = {}

    if (transportation_id) {
      filter.transportation_id = { $regex: transportation_id, $options: 'i' }
    }

    if (transportation_year) {
      filter.transportation_year = transportation_year
    } else if (transportation_years && transportation_years.length > 0) {
      filter.transportation_year = { $in: transportation_years }
    }

    if (date_out_start && date_out_end) {
      filter.date_out = {
        $gte: new Date(date_out_start),
        $lte: new Date(date_out_end),
      }
    } else if (date_out_start) {
      filter.date_out = { $gte: new Date(date_out_start) }
    } else if (date_out_end) {
      filter.date_out = { $lte: new Date(date_out_end) }
    }

    if (supplier) {
      filter.supplier = { $regex: supplier, $options: 'i' }
    } else if (suppliers && suppliers.length > 0) {
      filter.supplier = { $in: suppliers }
    }

    console.log('构建的数据库查询条件：', JSON.stringify(filter))

    // 计算分页参数
    const skip = (Number(page) - 1) * Number(limit)

    // 执行查询
    const [data, total] = await Promise.all([
      this.transportationModel
        .find(filter)
        .skip(skip)
        .limit(Number(limit))
        .sort({ date_out: -1 }) // 按发货日期倒序排列
        .exec(),
      this.transportationModel.countDocuments(filter).exec(),
    ])

    console.log(`查询结果：找到 ${total} 条记录`)
    return {
      total,
      page: Number(page),
      limit: Number(limit),
      data,
    }
  }

  // 获取单个发货信息
  async findOne(id: string): Promise<Transportation> {
    const transportation = await this.transportationModel.findById(id).exec()
    if (!transportation) {
      throw new NotFoundException(`发货信息ID ${id} 不存在`)
    }
    return transportation
  }

  // 更新发货信息
  async update(
    id: string,
    updateTransportationDto: UpdateTransportationDto,
    details?: Omit<CreateTransportationDetailDto, 'transportation_id'>[]
  ): Promise<Transportation> {
    try {
      console.log('更新发货信息，ID123456:', id)
      // 1. 更新发货信息主表
      const updatedTransportation = await this.transportationModel
        .findByIdAndUpdate(id, updateTransportationDto, { new: true })
        .exec()

      if (!updatedTransportation) {
        throw new NotFoundException(`发货信息ID ${id} 不存在`)
      }

      // 如果没有提供明细数据，则只更新主表信息
      if (!details || details.length === 0) {
        return updatedTransportation
      }

      // 2. 查找所有相关的发货明细，收集服装ID
      const transportation_id = updatedTransportation.transportation_id
      const existingDetails = await this.transportationDetailModel
        .find({ transportation_id: transportation_id })
        .exec()

      console.log(`找到 ${existingDetails.length} 条相关的发货明细记录`)

      // 3. 收集所有服装ID，用于更新发货数量
      const clothingIds = new Set<string>()
      const oemClothingIds = new Set<string>()

      // 收集现有明细中的服装ID
      for (const detail of existingDetails) {
        if (detail.oem === '是') {
          oemClothingIds.add(detail.clothing_id)
        } else {
          clothingIds.add(detail.clothing_id)
        }
      }

      // 4. 收集新明细中的服装ID
      for (const detail of details) {
        if (detail.oem === '是') {
          oemClothingIds.add(detail.clothing_id)
        } else {
          clothingIds.add(detail.clothing_id)
        }
      }

      console.log(
        `需要更新的普通服装数量: ${clothingIds.size}, OEM服装数量: ${oemClothingIds.size}`
      )

      // 5. 删除旧的发货明细
      const deleteDetailsResult = await this.transportationDetailModel
        .deleteMany({ transportation_id: transportation_id })
        .exec()

      console.log(`已删除 ${deleteDetailsResult.deletedCount} 条发货明细记录`)

      // 6. 新增新的发货明细
      const detailsToInsert = details.map((detail) => ({
        ...detail,
        transportation_id: transportation_id,
      }))

      await this.transportationDetailModel.insertMany(detailsToInsert)
      console.log(`已新增 ${detailsToInsert.length} 条发货明细记录`)

      // 7. 更新所有相关服装的发货数量
      for (const clothingId of clothingIds) {
        await this.updateClothingShipments(clothingId, '否')
      }

      for (const oemClothingId of oemClothingIds) {
        await this.updateClothingShipments(oemClothingId, '是')
      }

      // 8. 更新发货信息的总数量
      await this.updateTransportationTotals(transportation_id)

      return updatedTransportation
    } catch (error) {
      console.error('更新发货信息时出错:', error)
      throw error
    }
  }

  // 删除发货信息
  async remove(id: string): Promise<{ success: boolean; message: string }> {
    try {
      // 1. 查找发货信息
      const transportation = await this.transportationModel.findById(id).exec()
      if (!transportation) {
        throw new NotFoundException(`发货信息ID ${id} 不存在`)
      }
      const transportation_id = transportation.transportation_id

      // 2. 查找所有相关的发货明细，收集服装ID
      const details = await this.transportationDetailModel
        .find({ transportation_id: transportation_id })
        .exec()
      console.log(`找到 ${details.length} 条相关的发货明细记录`)

      // 收集所有服装ID，用于更新发货数量
      const clothingIds = new Set<string>()
      const oemClothingIds = new Set<string>()

      for (const detail of details) {
        if (detail.oem === '是') {
          oemClothingIds.add(detail.clothing_id)
        } else {
          clothingIds.add(detail.clothing_id)
        }
      }

      console.log(
        `需要更新的普通服装数量: ${clothingIds.size}, OEM服装数量: ${oemClothingIds.size}`
      )

      // 3. 删除相关的发货明细
      const deleteDetailsResult = await this.transportationDetailModel
        .deleteMany({ transportation_id: transportation_id })
        .exec()
      console.log(`已删除 ${deleteDetailsResult.deletedCount} 条发货明细记录`)

      // 4. 删除发货信息
      await this.transportationModel
        .findOneAndDelete({ transportation_id: transportation_id })
        .exec()

      // 5. 更新所有相关服装的发货数量
      for (const clothingId of clothingIds) {
        await this.updateClothingShipments(clothingId, '否')
      }

      for (const oemClothingId of oemClothingIds) {
        await this.updateClothingShipments(oemClothingId, '是')
      }

      return {
        success: true,
        message: `发货信息及其 ${deleteDetailsResult.deletedCount} 条明细记录已成功删除，并已更新相关服装的出货数量`,
      }
    } catch (error) {
      console.error('删除发货信息时出错:', error)
      throw error
    }
  }

  // 创建发货明细
  // async createDetail(
  //   createTransportationDetailDto: CreateTransportationDetailDto
  // ): Promise<TransportationDetail> {
  //   try {
  //     // 检查发货信息是否存在
  //     const transportation = await this.transportationModel
  //       .findOne({ transportation_id: createTransportationDetailDto.transportation_id })
  //       .exec()
  //     if (!transportation) {
  //       throw new NotFoundException(
  //         `发货信息ID ${createTransportationDetailDto.transportation_id} 不存在`
  //       )
  //     }

  //     // 创建发货明细
  //     const detail = new this.transportationDetailModel(createTransportationDetailDto)
  //     const savedDetail = await detail.save()

  //     // 更新服装的发货数量
  //     await this.updateClothingShipments(
  //       createTransportationDetailDto.clothing_id,
  //       createTransportationDetailDto.oem
  //     )

  //     return savedDetail
  //   } catch (error) {
  //     console.error('创建发货明细时出错:', error)
  //     throw error
  //   }
  // }

  // 批量创建发货明细
  async createDetailBatch(
    transportationId: string,
    details: Omit<CreateTransportationDetailDto, 'transportation_id'>[]
  ): Promise<TransportationDetail[]> {
    try {
      // 检查发货信息是否存在
      const transportation = await this.transportationModel.findById(transportationId).exec()
      if (!transportation) {
        throw new NotFoundException(`发货信息ID ${transportationId} 不存在`)
      }
      const transportation_id = transportation.transportation_id

      // 准备批量插入的数据
      const detailsToInsert = details.map((detail) => ({
        ...detail,
        transportation_id: transportation_id,
      }))

      // 批量插入发货明细
      const savedDetails = await this.transportationDetailModel.insertMany(detailsToInsert)

      // 收集所有服装ID，用于更新发货数量
      const clothingIds = new Set<string>()
      const oemClothingIds = new Set<string>()

      for (const detail of detailsToInsert) {
        if (detail.oem === '是') {
          oemClothingIds.add(detail.clothing_id)
        } else {
          clothingIds.add(detail.clothing_id)
        }
      }

      // 更新服装的发货数量
      for (const clothingId of clothingIds) {
        await this.updateClothingShipments(clothingId, '否')
      }

      // 更新OEM服装的发货数量
      for (const oemClothingId of oemClothingIds) {
        await this.updateClothingShipments(oemClothingId, '是')
      }

      // 更新发货信息的总数量
      await this.updateTransportationTotals(transportation_id)

      return savedDetails
    } catch (error) {
      console.error('批量创建发货明细时出错:', error)
      throw error
    }
  }

  // 获取指定发货的所有明细
  async findAllDetailsByTransportationId(
    transportationId: string
  ): Promise<TransportationDetail[]> {
    return this.transportationDetailModel
      .find({ transportation_id: transportationId })
      .sort({ series_number: 1 })
      .exec()
  }

  /**
   * 获取货运详情的处理后数据结构（适配小程序前端）
   * @param transportationId 货运单ID
   * @returns 处理后的货运详情数据
   */
  async getProcessedTransportationDetails(transportationId: string) {
    try {
      // 1. 获取货运单基本信息
      const transportation = await this.transportationModel
        .findOne({ transportation_id: transportationId })
        .exec()

      if (!transportation) {
        throw new Error('货运单不存在')
      }

      // 2. 获取货运详情 - 只返回需要的字段
      const details = await this.transportationDetailModel
        .find({ transportation_id: transportationId })
        .select(
          'transportation_id clothing_id clothing_name series_number QUP out_pcs oem package_quantity'
        )
        .sort({ series_number: 1 })
        .exec()

      if (!details || details.length === 0) {
        return {
          ...transportation.toObject(),
          detail: [],
          imgList: transportation.transportation_img?.map((img) => img.url) || [],
        }
      }

      // 3. 分离OEM服装和普通服装ID
      const oemClothingIds: string[] = []
      const clothingIds: string[] = []

      details.forEach((item) => {
        if (item.oem === '是') {
          oemClothingIds.push(item.clothing_id)
        } else {
          clothingIds.push(item.clothing_id)
        }
      })

      // 4. 获取服装图片信息
      let clothingImgList: any[] = []

      // 获取OEM服装图片
      if (oemClothingIds.length > 0) {
        const oemClothingList = await this.oemClothingModel
          .find({ oem_clothing_id: { $in: oemClothingIds } })
          .select('oem_clothing_id img')
          .exec()

        const oemImgList = oemClothingList.map((item) => ({
          clothing_id: item.oem_clothing_id,
          img: item.img?.[0]?.url || null,
        }))
        clothingImgList = clothingImgList.concat(oemImgList)
      }

      // 获取普通服装图片
      if (clothingIds.length > 0) {
        const clothingList = await this.clothingModel
          .find({ clothing_id: { $in: clothingIds } })
          .select('clothing_id img')
          .exec()

        const normalImgList = clothingList.map((item) => ({
          clothing_id: item.clothing_id,
          img: item.img?.[0]?.url || null,
        }))
        clothingImgList = clothingImgList.concat(normalImgList)
      }

      // 5. 按series_number分组处理，计算包裹数和剩余可入库数量
      const groupedBySeriesNumber: { [key: string]: any[] } = {}
      details.forEach((item) => {
        const seriesNumber = String(item.series_number || 0)
        if (!groupedBySeriesNumber[seriesNumber]) {
          groupedBySeriesNumber[seriesNumber] = []
        }
        groupedBySeriesNumber[seriesNumber].push(item)
      })

      const processedDetails: any[] = []

      for (const seriesNumber of Object.keys(groupedBySeriesNumber)) {
        const items = groupedBySeriesNumber[seriesNumber]

        if (items.length > 0) {
          const firstItem = items[0]
          const QUP = firstItem.QUP || 1

          // 计算该series_number下所有服装的总件数
          const totalPieces = items.reduce((sum, item) => sum + (item.out_pcs || 0), 0)

          // 包裹数 = 总件数 / QUP
          const packageQuantity = Math.ceil(totalPieces / QUP)

          // 查询已入库的包裹数量
          let totalInboundPackages = 0
          try {
            const inboundPackages = await this.packageModel
              .countDocuments({
                transportation_id: transportationId,
                series_number: parseInt(seriesNumber),
                status: { $in: ['in_stock', 'partially_shipped'] },
              })
              .exec()
            totalInboundPackages = inboundPackages
          } catch (error) {
            console.warn('查询已入库包裹数量失败:', error)
            totalInboundPackages = 0
          }

          // 计算剩余可入库数量
          const remainingQuantity = Math.max(0, packageQuantity - totalInboundPackages)

          console.log(
            `Series ${seriesNumber}: 总包裹数=${packageQuantity}, 已入库包裹数=${totalInboundPackages}, 剩余可入库数=${remainingQuantity}`
          )

          // 为每个服装添加图片信息并创建明细项
          for (const item of items) {
            const imgObj = clothingImgList.find((e) => e.clothing_id === item.clothing_id)

            processedDetails.push({
              transportation_id: item.transportation_id,
              clothing_id: item.clothing_id,
              clothing_name: item.clothing_name,
              series_number: item.series_number,
              QUP: QUP,
              out_pcs: item.out_pcs,
              oem: item.oem,
              img: imgObj?.img ? [imgObj.img] : [],
              package_quantity: packageQuantity, // 使用计算后的包裹数
              remainingQuantity: remainingQuantity, // 添加剩余可入库数量字段
              pieces_per_package: QUP, // 添加每包件数字段
              supplier: transportation.supplier, // 添加供应商字段
            })
          }
        }
      }

      // 6. 处理货运单图片
      const imgList = transportation.transportation_img?.map((img) => img.url) || []

      // 7. 返回处理后的数据
      const result = {
        ...transportation.toObject(),
        detail: processedDetails,
        imgList: imgList,
      }

      return result
    } catch (error) {
      throw new Error(`获取货运详情失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 获取货运详情的完整数据结构（货运明细页面专用，显示所有明细包括已入库的）
   * @param transportationId 货运单ID
   * @returns 处理后的货运详情数据
   */
  async getAllTransportationDetails(transportationId: string) {
    try {
      // 1. 获取货运单基本信息
      const transportation = await this.transportationModel
        .findOne({ transportation_id: transportationId })
        .exec()

      if (!transportation) {
        throw new Error('货运单不存在')
      }

      // 2. 获取货运详情 - 只返回需要的字段
      const details = await this.transportationDetailModel
        .find({ transportation_id: transportationId })
        .select(
          'transportation_id clothing_id clothing_name series_number QUP out_pcs oem package_quantity'
        )
        .sort({ series_number: 1 })
        .exec()

      if (!details || details.length === 0) {
        return {
          ...transportation.toObject(),
          detail: [],
          imgList: transportation.transportation_img?.map((img) => img.url) || [],
        }
      }

      // 3. 分离OEM服装和普通服装ID
      const oemClothingIds: string[] = []
      const clothingIds: string[] = []

      details.forEach((item) => {
        if (item.oem === '是') {
          oemClothingIds.push(item.clothing_id)
        } else {
          clothingIds.push(item.clothing_id)
        }
      })

      // 4. 获取服装图片信息
      let clothingImgList: any[] = []

      // 获取OEM服装图片
      if (oemClothingIds.length > 0) {
        const oemClothingList = await this.oemClothingModel
          .find({ oem_clothing_id: { $in: oemClothingIds } })
          .select('oem_clothing_id img')
          .exec()

        const oemImgList = oemClothingList.map((item) => ({
          clothing_id: item.oem_clothing_id,
          img: item.img?.[0]?.url || null,
        }))
        clothingImgList = clothingImgList.concat(oemImgList)
      }

      // 获取普通服装图片
      if (clothingIds.length > 0) {
        const clothingList = await this.clothingModel
          .find({ clothing_id: { $in: clothingIds } })
          .select('clothing_id img')
          .exec()

        const normalImgList = clothingList.map((item) => ({
          clothing_id: item.clothing_id,
          img: item.img?.[0]?.url || null,
        }))
        clothingImgList = clothingImgList.concat(normalImgList)
      }

      // 5. 为详情数据添加图片信息（显示所有明细，不过滤已入库的）
      const processedDetails = []

      for (const item of details) {
        const imgObj = clothingImgList.find((e) => e.clothing_id === item.clothing_id)

        // 货运明细页面：显示所有明细，包括已入库的
        processedDetails.push({
          transportation_id: item.transportation_id,
          clothing_id: item.clothing_id,
          clothing_name: item.clothing_name,
          series_number: item.series_number,
          QUP: item.QUP,
          out_pcs: item.out_pcs,
          oem: item.oem,
          img: imgObj?.img ? [imgObj.img] : [],
          package_quantity: (item as any).package_quantity,
          pieces_per_package: item.QUP,
          supplier: transportation.supplier,
        })
      }

      // 6. 处理货运单图片
      const imgList = transportation.transportation_img?.map((img) => img.url) || []

      // 7. 计算入库状态
      const inboundStatus = await this.calculateInboundStatus(transportationId)

      // 8. 返回处理后的数据
      const result = {
        ...transportation.toObject(),
        detail: processedDetails,
        imgList: imgList,
        inbound_status: inboundStatus,
      }

      return result
    } catch (error) {
      throw new Error(`获取货运详情失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  // 获取指定服装的发货明细
  async findAllDetailsByClothingId(clothingId: string): Promise<any[]> {
    // 使用聚合查询关联发货主表和明细表
    const details = await this.transportationDetailModel.aggregate([
      // 匹配指定的服装ID
      { $match: { clothing_id: clothingId } },
      // 关联发货主表
      {
        $lookup: {
          from: 'transportation', // 发货主表集合名
          localField: 'transportation_id',
          foreignField: 'transportation_id',
          as: 'transportation',
        },
      },
      // 展开发货主表数组（因为lookup返回的是数组）
      { $unwind: '$transportation' },
      // 投影需要的字段
      {
        $project: {
          _id: 1,
          transportation_id: 1,
          series_number: 1,
          clothing_name: 1,
          package_quantity: 1,
          QUP: 1,
          out_pcs: 1,
          oem: 1,
          clothing_id: 1,
          style: 1,
          createTime: 1,
          'transportation.date_out': 1,
          'transportation.supplier': 1,
          'transportation.transportation_id': 1,
          'transportation.remark': 1,
        },
      },
      // 按发货日期倒序排序
      { $sort: { 'transportation.date_out': -1 } },
    ])

    return details
  }

  // 获取单个发货明细
  // async findOneDetail(id: string): Promise<TransportationDetail> {
  //   const detail = await this.transportationDetailModel.findById(id).exec()
  //   if (!detail) {
  //     throw new NotFoundException(`发货明细ID ${id} 不存在`)
  //   }
  //   return detail
  // }

  // 更新发货明细
  async updateDetail(
    id: string,
    updateTransportationDetailDto: UpdateTransportationDetailDto
  ): Promise<TransportationDetail> {
    try {
      // 获取原始明细记录
      console.log('更新发货明细，ID123456:', id)
      const originalDetail = await this.transportationDetailModel.findById(id).exec()
      if (!originalDetail) {
        throw new NotFoundException(`发货明细ID ${id} 不存在`)
      }

      // 更新明细
      const updatedDetail = await this.transportationDetailModel
        .findByIdAndUpdate(id, updateTransportationDetailDto, { new: true })
        .exec()

      if (!updatedDetail) {
        throw new NotFoundException(`更新发货明细失败，ID ${id} 不存在`)
      }

      // 如果服装ID或OEM状态发生变化，需要更新两个服装的发货数量
      if (
        originalDetail.clothing_id !== updatedDetail.clothing_id ||
        originalDetail.oem !== updatedDetail.oem
      ) {
        // 更新原始服装的发货数量
        await this.updateClothingShipments(originalDetail.clothing_id, originalDetail.oem)
        // 更新新服装的发货数量
        await this.updateClothingShipments(updatedDetail.clothing_id, updatedDetail.oem)
      } else if (originalDetail.out_pcs !== updatedDetail.out_pcs) {
        // 如果只是数量变化，只需更新当前服装的发货数量
        await this.updateClothingShipments(updatedDetail.clothing_id, updatedDetail.oem)
      }

      // 更新发货信息的总数量
      if (originalDetail.out_pcs !== updatedDetail.out_pcs) {
        await this.updateTransportationTotals(updatedDetail.transportation_id.toString())
      }

      return updatedDetail
    } catch (error) {
      console.error('更新发货明细时出错:', error)
      throw error
    }
  }

  // 删除发货明细
  // async removeDetail(id: string): Promise<{ success: boolean; message: string }> {
  //   try {
  //     // 获取明细记录
  //     const detail = await this.transportationDetailModel.findById(id).exec()
  //     if (!detail) {
  //       throw new NotFoundException(`发货明细ID ${id} 不存在`)
  //     }

  //     // 删除明细
  //     await this.transportationDetailModel.findByIdAndDelete(id).exec()

  //     // 保存服装ID，以便在发生错误时仍能更新发货数量
  //     const clothingId = detail.clothing_id
  //     const oem = detail.oem
  //     const transportationId = detail.transportation_id.toString()

  //     // 更新服装的发货数量
  //     await this.updateClothingShipments(clothingId, oem)

  //     // 更新发货信息的总数量
  //     await this.updateTransportationTotals(transportationId)

  //     return {
  //       success: true,
  //       message: '发货明细已成功删除',
  //     }
  //   } catch (error) {
  //     console.error('删除发货明细时出错:', error)
  //     throw error
  //   }
  // }

  // 获取年份选项列表
  async getYearOptions(suppliers?: string): Promise<string[]> {
    const filter: any = {}

    // 如果提供了供应商过滤条件
    if (suppliers) {
      const supplierArray = suppliers.split(',')
      if (supplierArray.length > 0) {
        filter.supplier = { $in: supplierArray }
      }
    }

    const years = await this.transportationModel.distinct('transportation_year', filter).exec()

    // 按年份降序排序
    return years.sort((a, b) => b.localeCompare(a))
  }

  // 获取供应商选项列表
  async getSupplierOptions(years?: string): Promise<string[]> {
    const filter: any = {}

    // 如果提供了年份过滤条件
    if (years) {
      const yearArray = years.split(',')
      if (yearArray.length > 0) {
        filter.transportation_year = { $in: yearArray }
      }
    }

    return this.transportationModel.distinct('supplier', filter).exec()
  }

  /**
   * 小程序专用：获取货运单列表
   * @param params 查询参数
   * @returns 分页的货运单列表
   */
  async getMiniProgramTransportationList(params: {
    page: number
    limit: number
    supplier?: string
    arrived?: string // "0" 未到货, "1" 已到货, "" 全部
    inbound_status?: string // full_inbound, partial_inbound, not_inbound, "" 全部
  }) {
    try {
      const { page = 1, limit = 10, supplier, arrived, inbound_status } = params

      // 构建查询条件
      const filter: any = {}

      // 供应商筛选
      if (supplier && supplier.trim() !== '') {
        filter.supplier = { $regex: supplier, $options: 'i' }
      }

      // 到货状态筛选
      if (arrived === '0') {
        // 未到货：date_arrived 为 null 或不存在
        filter.date_arrived = { $eq: null }
      } else if (arrived === '1') {
        // 已到货：date_arrived 不为 null
        filter.date_arrived = { $ne: null }
      }
      // arrived 为空字符串或未提供时，不添加筛选条件（显示全部）

      // 入库状态筛选
      if (inbound_status === '0') {
        // 未入库
        filter.inbound_status = { $ne: 'full_inbound' }
      }

      console.log('小程序货运单查询条件：', JSON.stringify(filter))

      // 计算分页参数
      const skip = (Number(page) - 1) * Number(limit)

      // 执行查询
      const [data, total] = await Promise.all([
        this.transportationModel
          .find(filter)
          .skip(skip)
          .limit(Number(limit))
          .sort({ date_out: -1 }) // 按发货日期倒序排列
          .exec(),
        this.transportationModel.countDocuments(filter).exec(),
      ])

      console.log(`小程序查询结果：找到 ${total} 条记录，当前页 ${page}，每页 ${limit} 条`)

      return {
        code: 200,
        total,
        page: Number(page),
        limit: Number(limit),
        data,
        message: '获取货运单列表成功',
      }
    } catch (error) {
      console.error('获取小程序货运单列表失败:', error)
      return {
        code: 500,
        data: null,
        message: `获取货运单列表失败: ${error instanceof Error ? error.message : '未知错误'}`,
      }
    }
  }

  /**
   * 获取小程序供应商列表，并按照供应商名称在数据库中出现的次数从高到低排序。
   * 同时会过滤掉空值或只包含空格的供应商名称。
   * @returns 包含供应商列表、状态码和消息的响应对象。
   */
  async getMiniProgramSupplierOptions() {
    try {
      const aggregateResult = await this.transportationModel
        .aggregate([
          {
            // 阶段 1: $match - 初始过滤，确保处理有效数据
            // 过滤掉 supplier 字段为 null、空字符串或只包含空格的文档。
            // 同时确保 transportation_year 字段存在且为数字类型。
            $match: {
              supplier: {
                $nin: [null, ''], // 排除 null 和空字符串
                $exists: true, // 确保字段存在
                $regex: /\S/, // 确保字符串中至少包含一个非空白字符
              },
              transportation_year: { $type: 'string' }, // 确保年份字段是数字
            },
          },
          {
            // 阶段 2: $group - 按 (supplier, year) 组合分组并计算次数
            // 这一步会为每个唯一的 (供应商名称, 年份) 组合计算其出现次数。
            $group: {
              _id: {
                supplier: '$supplier',
                year: '$transportation_year',
              },
              count: { $sum: 1 }, // 计算该组合的文档数量
            },
          },
          {
            // 阶段 3: $sort - 核心排序逻辑
            // 首先按年份降序 (最新年份优先)，然后在相同年份下按出现次数降序 (次数多优先)。
            $sort: {
              '_id.year': -1, // 优先：年份降序
              count: -1, // 次要：次数降序
            },
          },
          {
            // 阶段 4: $group - 收集排序后的供应商名称
            // 将所有排序好的供应商名称（包括重复的）按顺序推入一个数组。
            // 这里的 _id: null 表示所有文档都会被合并到同一个组中。
            $group: {
              _id: null,
              // $push 操作会按照管道当前的顺序将供应商名称推入 'orderedSuppliers' 数组
              orderedSuppliers: { $push: '$_id.supplier' },
            },
          },
          {
            // 阶段 5: $project - 最终输出字段选择
            // 只保留包含有序供应商名称的数组。
            $project: {
              _id: 0, // 排除默认的 _id 字段
              orderedSuppliers: 1, // 包含我们刚刚创建的数组
            },
          },
        ])
        .exec()

      // 在 Node.js 端进行去重并保持顺序
      let finalSortedUniqueSuppliers: string[] = []
      if (aggregateResult.length > 0) {
        // 聚合结果中会包含一个文档，其中有 orderedSuppliers 数组
        const rawSuppliers = aggregateResult[0].orderedSuppliers
        const seenSuppliers = new Set<string>() // 用于高效去重

        // 遍历原始排序数组，只添加第一次遇到的供应商，从而去重并保持排序优先级
        for (const supplier of rawSuppliers) {
          if (!seenSuppliers.has(supplier)) {
            finalSortedUniqueSuppliers.push(supplier)
            seenSuppliers.add(supplier)
          }
        }
      }

      return {
        code: 200,
        data: finalSortedUniqueSuppliers,
        message: '获取供应商列表成功',
      }
    } catch (error) {
      console.error('获取小程序供应商列表失败:', error)
      return {
        code: 500,
        data: [],
        message: `获取供应商列表失败: ${error instanceof Error ? error.message : '未知错误'}`,
      }
    }
  }

  // 更新到货日期（为微信小程序提供）
  async updateArrivedDate(
    transportation_id: string,
    arrived_date: string
  ): Promise<{ code: number; message: string }> {
    try {
      let arrivedDate: Date | null
      const previousTransportation = await this.transportationModel
        .findOne({ transportation_id: transportation_id })
        .exec()

      if (!previousTransportation) {
        return {
          code: 404,
          message: '发货信息不存在',
        }
      }

      if (arrived_date === '' || arrived_date === null) {
        arrivedDate = null
      } else {
        arrivedDate = new Date(arrived_date)
      }

      // 检查是否是首次设置到货日期（从null变为有值）
      const isFirstTimeArrival = !previousTransportation.date_arrived && arrivedDate
      // 检查是否是取消到货日期（从有值变为null）
      const isCancelArrival = previousTransportation.date_arrived && !arrivedDate

      const updatedTransportation = await this.transportationModel
        .findOneAndUpdate(
          { transportation_id: transportation_id },
          { date_arrived: arrivedDate },
          { new: true }
        )
        .exec()
      console.log('更新后的发货信息:', updatedTransportation)

      if (!updatedTransportation) {
        return {
          code: 404,
          message: '发货信息不存在',
        }
      }

      // 如果是首次设置到货日期，增加相关服装的到货数
      if (isFirstTimeArrival) {
        await this.updateClothingArrivalQuantity(transportation_id, 'add')
      }
      // 如果是取消到货日期，减少相关服装的到货数
      else if (isCancelArrival) {
        await this.updateClothingArrivalQuantity(transportation_id, 'subtract')
      }

      return {
        code: 200,
        message: '更新成功',
      }
    } catch (error) {
      console.error('更新到货日期失败:', error)
      return {
        code: 500,
        message: '更新失败',
      }
    }
  }

  // 更新服装到货数的私有方法
  private async updateClothingArrivalQuantity(
    transportation_id: string,
    operation: 'add' | 'subtract' = 'add'
  ): Promise<void> {
    try {
      // 获取该货运单的所有明细
      const transportationDetails = await this.transportationDetailModel
        .find({ transportation_id })
        .exec()

      console.log(`找到 ${transportationDetails.length} 条货运明细`)

      // 按服装ID分组统计数量
      const clothingUpdates = new Map<string, number>()
      const oemClothingUpdates = new Map<string, number>()

      transportationDetails.forEach((detail) => {
        const quantity = detail.out_pcs || 0

        // 根据SKU判断是否为OEM服装
        if (detail.clothing_id && detail.clothing_id.includes('_')) {
          const parts = detail.clothing_id.split('_')
          if (parts[0] === 'oem') {
            // OEM服装
            const oemClothingId = detail.clothing_id
            const currentQuantity = oemClothingUpdates.get(oemClothingId) || 0
            oemClothingUpdates.set(oemClothingId, currentQuantity + quantity)
          } else {
            // 普通服装
            const clothingId = detail.clothing_id
            const currentQuantity = clothingUpdates.get(clothingId) || 0
            clothingUpdates.set(clothingId, currentQuantity + quantity)
          }
        } else {
          // 普通服装
          const clothingId = detail.clothing_id
          const currentQuantity = clothingUpdates.get(clothingId) || 0
          clothingUpdates.set(clothingId, currentQuantity + quantity)
        }
      })

      // 根据操作类型确定数量的符号
      const multiplier = operation === 'add' ? 1 : -1

      // 批量更新普通服装的到货数
      for (const [clothingId, quantity] of clothingUpdates) {
        const adjustedQuantity = quantity * multiplier
        await this.clothingModel
          .findOneAndUpdate(
            { clothing_id: clothingId },
            { $inc: { arrival_quantity: adjustedQuantity } },
            { new: true }
          )
          .exec()
        console.log(`更新服装 ${clothingId} 到货数 ${operation === 'add' ? '+' : '-'}${quantity}`)
      }

      // 批量更新OEM服装的到货数
      for (const [oemClothingId, quantity] of oemClothingUpdates) {
        const adjustedQuantity = quantity * multiplier
        await this.oemClothingModel
          .findOneAndUpdate(
            { oem_clothing_id: oemClothingId },
            { $inc: { arrival_quantity: adjustedQuantity } },
            { new: true }
          )
          .exec()
        console.log(
          `更新OEM服装 ${oemClothingId} 到货数 ${operation === 'add' ? '+' : '-'}${quantity}`
        )
      }

      console.log('服装到货数更新完成')
    } catch (error) {
      console.error('更新服装到货数失败:', error)
      throw error
    }
  }

  // 获取最新的发货编码
  async getLatestTransportationId(): Promise<string> {
    try {
      // 查询最新的发货信息，按照发货编码倒序排序
      const latestTransportation = await this.transportationModel
        .findOne()
        .sort({ transportation_id: -1 })
        .select('transportation_id')
        .exec()

      if (latestTransportation && latestTransportation.transportation_id) {
        console.log('找到最新的发货编码:', latestTransportation.transportation_id)
        return latestTransportation.transportation_id
      } else {
        // 如果没有找到任何发货信息，返回默认值
        const currentYear = new Date().getFullYear()
        const defaultId = `FH${currentYear}001`
        console.log('没有找到发货信息，返回默认编码:', defaultId)
        return defaultId
      }
    } catch (error) {
      console.error('获取最新发货编码时出错:', error)
      // 出错时返回默认值
      const currentYear = new Date().getFullYear()
      return `FH${currentYear}001`
    }
  }

  // 导出选中发货明细
  async exportSelectedTransportationDetails(transportation_ids: string[]): Promise<any> {
    try {
      console.log('导出选中发货明细，发货编码:', transportation_ids)

      // 存储普通服装和OEM服装的编码数组
      const normalClothingIds: string[] = []
      const oemClothingIds: string[] = []

      // 存储所有发货明细，按发货编码顺序
      const allDetails: any[] = []

      // 1. 按发货编码的顺序，查询发货明细
      for (const transportation_id of transportation_ids) {
        // 查询发货信息
        const transportation = await this.transportationModel
          .findOne({ transportation_id: transportation_id })
          .exec()
        if (!transportation) {
          console.warn(`发货信息ID ${transportation_id} 不存在，跳过`)
          continue
        }

        // 查询该发货的所有明细
        const details = await this.transportationDetailModel
          .find({ transportation_id: transportation_id })
          .sort({ series_number: 1 })
          .exec()

        // 将发货日期添加到每个明细中
        const detailsWithDate = details.map((detail) => ({
          ...detail.toObject(),
          date_out: transportation.date_out,
        }))

        // 添加到总明细列表
        allDetails.push(...detailsWithDate)

        // 2. 将服装编码按是否为OEM分类存入两个数组，若已存在则跳过
        for (const detail of details) {
          if (detail.oem === '是') {
            if (!oemClothingIds.includes(detail.clothing_id)) {
              oemClothingIds.push(detail.clothing_id)
            }
          } else {
            if (!normalClothingIds.includes(detail.clothing_id)) {
              normalClothingIds.push(detail.clothing_id)
            }
          }
        }
      }

      // 3. 根据服装编码查询服装信息
      const normalClothingInfo = await Promise.all(
        normalClothingIds.map(async (clothingId) => {
          const clothing = await this.clothingModel.findOne({ clothing_id: clothingId }).exec()
          return clothing
            ? {
                clothing_id: clothingId,
                clothing_name: clothing.clothing_name,
                clipping_pcs: clothing.clipping_pcs || 0,
                shipments: clothing.shipments || 0,
                oem: '否',
              }
            : null
        })
      )

      const oemClothingInfo = await Promise.all(
        oemClothingIds.map(async (clothingId) => {
          const clothing = await this.oemClothingModel
            .findOne({ oem_clothing_id: clothingId })
            .exec()
          return clothing
            ? {
                clothing_id: clothingId,
                clothing_name: clothing.oem_clothing_name,
                in_pcs: clothing.in_pcs || 0,
                shipments: clothing.shipments || 0,
                oem: '是',
              }
            : null
        })
      )

      // 合并服装信息，过滤掉null值
      const clothingInfo = [
        ...normalClothingInfo.filter((item) => item !== null),
        ...oemClothingInfo.filter((item) => item !== null),
      ]

      // 按发货日期排序明细
      allDetails.sort((a, b) => {
        return new Date(a.date_out).getTime() - new Date(b.date_out).getTime()
      })

      return {
        details: allDetails,
        clothingInfo: clothingInfo,
      }
    } catch (error) {
      console.error('导出选中发货明细时出错:', error)
      throw error
    }
  }

  // 更新服装的发货数量
  private async updateClothingShipments(clothingId: string, oem?: string): Promise<void> {
    try {
      // 根据OEM状态决定更新哪个集合
      if (oem === '是') {
        // 更新OEM服装的发货数量
        const result = await this.transportationDetailModel
          .aggregate([
            { $match: { clothing_id: clothingId, oem: '是' } },
            { $group: { _id: null, total_pcs: { $sum: '$out_pcs' } } },
          ])
          .exec()

        const totalPcs = result.length > 0 ? result[0].total_pcs : 0

        await this.oemClothingModel
          .updateOne({ oem_clothing_id: clothingId }, { $set: { shipments: totalPcs } })
          .exec()

        console.log(`已更新OEM服装 ${clothingId} 的发货数量为 ${totalPcs}`)
      } else {
        // 更新普通服装的发货数量
        const result = await this.transportationDetailModel
          .aggregate([
            { $match: { clothing_id: clothingId, $or: [{ oem: { $ne: '是' } }, { oem: null }] } },
            { $group: { _id: null, total_pcs: { $sum: '$out_pcs' } } },
          ])
          .exec()

        const totalPcs = result.length > 0 ? result[0].total_pcs : 0

        await this.clothingModel
          .updateOne({ clothing_id: clothingId }, { $set: { shipments: totalPcs } })
          .exec()

        console.log(`已更新服装 ${clothingId} 的发货数量为 ${totalPcs}`)
      }
    } catch (error) {
      console.error(`更新服装 ${clothingId} 的发货数量时出错:`, error)
      // 继续处理，不中断整个过程
    }
  }

  // 更新发货信息的总数量
  private async updateTransportationTotals(transportation_id: string): Promise<void> {
    try {
      // 计算总件数
      const result = await this.transportationDetailModel
        .aggregate([
          { $match: { transportation_id: transportation_id } },
          {
            $group: {
              _id: null,
              total_pcs: { $sum: '$out_pcs' },
              total_package_quantity: { $sum: '$package_quantity' },
            },
          },
        ])
        .exec()

      if (result.length > 0) {
        const { total_pcs, total_package_quantity } = result[0]

        // 更新发货信息
        await this.transportationModel
          .updateOne(
            { transportation_id: transportation_id },
            {
              $set: {
                total_pcs,
                total_package_quantity: Math.round(total_package_quantity),
              },
            }
          )
          .exec()

        console.log(
          `已更新发货信息 ${transportation_id} 的总数量为 ${total_pcs}，总包裹数为 ${total_package_quantity}`
        )
      }
    } catch (error) {
      console.error(`更新发货信息 ${transportation_id} 的总数量时出错:`, error)
      // 继续处理，不中断整个过程
    }
  }

  // 修改价格 (为微信小程序提供)
  async updatePrice(
    transportation_id: string,
    price: number
  ): Promise<{ code: number; message: string }> {
    try {
      await this.transportationModel
        .updateOne({ transportation_id: transportation_id }, { $set: { price: price } })
        .exec()

      return {
        code: 200,
        message: '修改成功',
      }
    } catch (error) {
      console.error(`修改发货信息 ${transportation_id} 的价格时出错:`, error)
      return {
        code: 500,
        message: '修改失败',
      }
    }
  }

  /**
   * 计算货运单的入库状态
   */
  private async calculateInboundStatus(transportation_id: string): Promise<string> {
    try {
      // 1. 获取货运单的总件数
      const transportation = await this.transportationModel
        .findOne({ transportation_id })
        .select('total_pcs')
        .exec()

      if (!transportation || !transportation.total_pcs) {
        return 'not_inbound'
      }

      // 2. 查询已入库的总件数
      const inboundStats = await this.packageModel
        .aggregate([
          {
            $match: {
              transportation_id: transportation_id,
              status: { $in: ['in_stock', 'partially_shipped'] },
            },
          },
          {
            $unwind: '$contents',
          },
          {
            $group: {
              _id: null,
              total_inbound_pieces: { $sum: '$contents.current_quantity' },
            },
          },
        ])
        .exec()

      const totalInboundPieces = inboundStats[0]?.total_inbound_pieces || 0

      // 3. 计算入库状态
      if (totalInboundPieces === 0) {
        return 'not_inbound'
      } else if (totalInboundPieces >= transportation.total_pcs) {
        return 'full_inbound'
      } else {
        return 'partial_inbound'
      }
    } catch (error) {
      console.error('计算入库状态失败:', error)
      return 'not_inbound'
    }
  }

  /**
   * 包裹初始化 - 批量创建包裹库存记录
   */
  async initializePackages(packageInitializationDto: PackageInitializationDto): Promise<PackageInitializationResponseDto> {
    try {
      const { packages, operator = 'system', notes } = packageInitializationDto

      // 生成入库批次号
      const batchCode = `INIT-${new Date().toISOString().split('T')[0].replace(/-/g, '')}-${Date.now()}`

      const createdPackages: string[] = []
      const failedPackages: Array<{ package_code: string; reason: string }> = []

      console.log(`开始包裹初始化，共 ${packages.length} 个包裹`)

      for (const packageItem of packages) {
        try {
          // 1. 验证包裹编码是否已存在
          const existingPackage = await this.packageModel.findOne({
            package_code: packageItem.package_code
          })

          if (existingPackage) {
            failedPackages.push({
              package_code: packageItem.package_code,
              reason: '包裹编码已存在'
            })
            continue
          }

          // 2. 验证仓库是否存在
          const warehouse = await this.warehouseModel.findOne({
            warehouse_id: packageItem.warehouse_id
          })

          if (!warehouse) {
            failedPackages.push({
              package_code: packageItem.package_code,
              reason: `仓库 ${packageItem.warehouse_id} 不存在`
            })
            continue
          }

          // 3. 查找或创建产品记录
          let product = await this.productModel.findOne({ sku: packageItem.sku })

          if (!product) {
            // 自动创建产品记录
            const isOem = packageItem.oem_clothing_id ? true : false
            product = new this.productModel({
              sku: packageItem.sku,
              name: packageItem.clothing_name,
              unit: '件',
              clothing_id: packageItem.clothing_id,
              oem_clothing_id: packageItem.oem_clothing_id,
              is_oem: isOem,
              status: 'active'
            })
            await product.save()
            console.log(`自动创建产品记录: ${packageItem.sku}`)
          }

          // 4. 生成分类码
          const classificationCode = `${packageItem.sku}_${packageItem.original_quantity}`

          // 5. 创建包裹记录
          const newPackage = new this.packageModel({
            package_code: packageItem.package_code,
            classification_code: classificationCode,
            package_type: 'single', // 初始化的包裹默认为单一包裹
            inbound_batch_code: batchCode,
            warehouse_id: packageItem.warehouse_id,
            location_code: packageItem.location_code,
            status: 'in_stock',
            remaining_percentage: packageItem.current_quantity / packageItem.original_quantity,
            contents: [{
              product_id: product._id,
              sku: packageItem.sku,
              name: packageItem.clothing_name,
              original_quantity: packageItem.original_quantity,
              current_quantity: packageItem.current_quantity,
              clothing_id: packageItem.clothing_id,
              oem_clothing_id: packageItem.oem_clothing_id,
              is_oem: packageItem.oem_clothing_id ? true : false
            }],
            transportation_id: packageItem.transportation_id,
            series_number: packageItem.series_number,
            supplier: packageItem.supplier,
            inbound_at: packageItem.inbound_date ? new Date(packageItem.inbound_date) : new Date()
          })

          await newPackage.save()
          createdPackages.push(packageItem.package_code)

          console.log(`成功创建包裹: ${packageItem.package_code}`)

        } catch (error) {
          console.error(`创建包裹 ${packageItem.package_code} 失败:`, error)
          failedPackages.push({
            package_code: packageItem.package_code,
            reason: error instanceof Error ? error.message : '未知错误'
          })
        }
      }

      // 6. 更新服装库存数据（如果有关联的服装ID）
      await this.updateClothingStockForInitialization(packages.filter(p =>
        createdPackages.includes(p.package_code)
      ))

      const result: PackageInitializationResponseDto = {
        code: 200,
        message: failedPackages.length > 0
          ? `包裹初始化部分成功：${createdPackages.length}个成功，${failedPackages.length}个失败`
          : '包裹初始化成功',
        data: {
          total_requested: packages.length,
          successful_created: createdPackages.length,
          failed_created: failedPackages.length,
          batch_code: batchCode,
          created_packages: createdPackages,
          failed_packages: failedPackages
        }
      }

      console.log('包裹初始化完成:', result)
      return result

    } catch (error) {
      console.error('包裹初始化失败:', error)
      throw new HttpException(
        error instanceof Error ? error.message : '包裹初始化失败',
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  /**
   * 为包裹初始化更新服装库存数据
   */
  private async updateClothingStockForInitialization(packages: any[]) {
    try {
      // 按服装ID分组统计数量
      const clothingQuantityMap = new Map<string, number>()
      const oemClothingQuantityMap = new Map<string, number>()

      for (const packageItem of packages) {
        if (packageItem.clothing_id) {
          const currentQuantity = clothingQuantityMap.get(packageItem.clothing_id) || 0
          clothingQuantityMap.set(packageItem.clothing_id, currentQuantity + packageItem.current_quantity)
        }

        if (packageItem.oem_clothing_id) {
          const currentQuantity = oemClothingQuantityMap.get(packageItem.oem_clothing_id) || 0
          oemClothingQuantityMap.set(packageItem.oem_clothing_id, currentQuantity + packageItem.current_quantity)
        }
      }

      // 更新普通服装库存
      for (const [clothingId, quantity] of clothingQuantityMap) {
        await this.clothingModel.updateOne(
          { clothing_id: clothingId },
          { $inc: { stock_quantity: quantity } }
        )
        console.log(`更新服装 ${clothingId} 库存数 +${quantity}`)
      }

      // 更新OEM服装库存
      for (const [oemClothingId, quantity] of oemClothingQuantityMap) {
        await this.oemClothingModel.updateOne(
          { oem_clothing_id: oemClothingId },
          { $inc: { stock_quantity: quantity } }
        )
        console.log(`更新OEM服装 ${oemClothingId} 库存数 +${quantity}`)
      }

    } catch (error) {
      console.error('更新服装库存失败:', error)
      // 不抛出错误，避免影响主流程
    }
  }
}
